//FUNCIONES

//Declaración de función con tipado explicito:
function sumar(a: number, b: number): number {
    return a + b;
}

//Funciones flecha con retorno implicito (inferido por typeScript)
const dividir = (a: number, b: number): number => a / b;

//Funciones con parametros opcionales:
function saludar (nombre: string, edad?: string): string {
    if(edad === undefined){
        return `Hola, mi nombre es ${nombre} y tengo ${edad} años`;
    }else{
        return `Hola, mi nombre es ${nombre}`;
    }
}

//Funciones con parámetros por defecto:
function saludar2 (nombre: string, edad: number = 30): string {
    return `Hola, mi nombre es ${nombre} y tengo ${edad} años`;
}