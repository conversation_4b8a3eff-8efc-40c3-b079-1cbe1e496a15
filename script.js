//FUNCIONES
//Declaración de función con tipado explicito:
function sumar(a, b) {
    return a + b;
}
//Funciones flecha con retorno implicito (inferido por Typescript):
var dividir = function (a, b) { return a / b; };
//Funciones con parámetros opcionales:
function saludar(nombre, edad) {
    if (edad !== undefined) {
        return "Holan mi nombre es ".concat(nombre, " y tengo ").concat(edad, " a\u00F1os.");
    }
    else {
        return "Holan mi nombre es ".concat(nombre, ".");
    }
}
//Funciones con parámetros por defecto:
function saludar2(nombre, edad) {
    if (edad === void 0) { edad = 30; }
    return "Holan mi nombre es ".concat(nombre, " y tengo ").concat(edad, " a\u00F1os.");
}
